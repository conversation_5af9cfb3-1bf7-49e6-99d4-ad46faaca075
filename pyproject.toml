[project]
name = "vue-fastapi-admin"
version = "0.1.0"
description = "Vue Fastapi admin"
authors = [
    {name = "mizhexiaoxiao", email = "<EMAIL>"},
]
requires-python = ">=3.11"
dependencies = [
    "fastapi==0.111.0",
    "tortoise-orm==0.23.0",
    "pydantic==2.10.5",
    "email-validator==2.2.0",
    "passlib==1.7.4",
    "pyjwt==2.10.1",
    "black==24.10.0",
    "isort==5.13.2",
    "ruff==0.9.1",
    "loguru==0.7.3",
    "pydantic-settings==2.7.1",
    "argon2-cffi==23.1.0",
    "pydantic-core==2.27.2",
    "annotated-types==0.7.0",
    "setuptools==75.8.0",
    "uvicorn==0.34.0",
    "h11==0.14.0",
    "aerich==0.8.1",
    "aiosqlite==0.20.0",
    "anyio==4.8.0",
    "argon2-cffi-bindings==21.2.0",
    "asyncclick==8.1.8",
    "certifi==2024.12.14",
    "cffi==1.17.1",
    "click==8.1.8",
    "dictdiffer==0.9.0",
    "dnspython==2.7.0",
    "fastapi-cli==0.0.7",
    "httpcore==1.0.7",
    "httptools==0.6.4",
    "httpx==0.28.1",
    "idna==3.10",
    "iso8601==2.1.0",
    "jinja2==3.1.5",
    "markdown-it-py==3.0.0",
    "markupsafe==3.0.2",
    "mdurl==0.1.2",
    "mypy-extensions==1.0.0",
    "orjson==3.10.14",
    "packaging==24.2",
    "pathspec==0.12.1",
    "platformdirs==4.3.6",
    "pycparser==2.22",
    "pygments==2.19.1",
    "pypika-tortoise==0.3.2",
    "python-dotenv==1.0.1",
    "python-multipart==0.0.20",
    "pytz==2024.2",
    "pyyaml==6.0.2",
    "rich==13.9.4",
    "rich-toolkit==0.13.2",
    "shellingham==1.5.4",
    "sniffio==1.3.1",
    "starlette==0.37.2",
    "typer==0.15.1",
    "typing-extensions==4.12.2",
    "ujson==5.10.0",
    "uvloop==0.21.0",
    "watchfiles==1.0.4",
    "websockets==14.1",
    "pyproject-toml>=0.1.0",
    "uvloop==0.21.0 ; sys_platform != 'win32'",
]

[tool.black]
line-length = 120
target-version = ["py310", "py311"]

[tool.ruff]
line-length = 120
lint.extend-select = []
lint.ignore = [
    "F403",
    "F405",
]

[tool.aerich]
tortoise_orm = "app.settings.TORTOISE_ORM"
location = "./migrations"
src_folder = "./."
