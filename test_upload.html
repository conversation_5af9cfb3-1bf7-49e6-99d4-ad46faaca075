<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件上传测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            background-color: #f9f9f9;
        }
        .upload-area.dragover {
            border-color: #007bff;
            background-color: #e7f3ff;
        }
        .file-info {
            background-color: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .buttons {
            text-align: center;
            margin: 20px 0;
        }
        button {
            padding: 10px 20px;
            margin: 0 10px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .progress {
            width: 100%;
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            background-color: #007bff;
            transition: width 0.3s ease;
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <h1>文件上传测试页面</h1>
    
    <div class="upload-area" id="uploadArea">
        <p id="uploadText">点击选择文件或拖拽文件到此处</p>
        <p><small>仅支持 .docx 格式文件，最大 10MB</small></p>
        <input type="file" id="fileInput" accept=".docx" style="display: none;">
    </div>
    
    <div id="fileInfo" class="file-info hidden">
        <h3>已选择文件:</h3>
        <p id="fileName"></p>
        <p id="fileSize"></p>
    </div>
    
    <div class="buttons">
        <button id="selectBtn" class="btn-secondary">选择文件</button>
        <button id="processBtn" class="btn-primary hidden">开始处理</button>
        <button id="resetBtn" class="btn-secondary hidden">重新选择</button>
        <button id="downloadBtn" class="btn-success hidden">下载文档</button>
    </div>
    
    <div id="progressContainer" class="hidden">
        <p>处理进度: <span id="progressText">0%</span></p>
        <div class="progress">
            <div class="progress-bar" id="progressBar" style="width: 0%"></div>
        </div>
    </div>
    
    <div id="status"></div>

    <script>
        let selectedFile = null;
        let userDocId = null;
        let isProcessing = false;
        
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const fileInfo = document.getElementById('fileInfo');
        const fileName = document.getElementById('fileName');
        const fileSize = document.getElementById('fileSize');
        const selectBtn = document.getElementById('selectBtn');
        const processBtn = document.getElementById('processBtn');
        const resetBtn = document.getElementById('resetBtn');
        const downloadBtn = document.getElementById('downloadBtn');
        const progressContainer = document.getElementById('progressContainer');
        const progressBar = document.getElementById('progressBar');
        const progressText = document.getElementById('progressText');
        const status = document.getElementById('status');
        const uploadText = document.getElementById('uploadText');
        
        // 文件选择事件
        fileInput.addEventListener('change', handleFileSelect);
        selectBtn.addEventListener('click', () => fileInput.click());
        processBtn.addEventListener('click', startProcessing);
        resetBtn.addEventListener('click', resetUpload);
        downloadBtn.addEventListener('click', downloadDocument);
        
        // 拖拽事件
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        });
        
        uploadArea.addEventListener('click', () => {
            if (!selectedFile && !isProcessing) {
                fileInput.click();
            }
        });
        
        function handleFileSelect(e) {
            const file = e.target.files[0];
            if (file) {
                handleFile(file);
            }
        }
        
        function handleFile(file) {
            if (!file.name.toLowerCase().endsWith('.docx')) {
                alert('仅支持 .docx 格式文件');
                return;
            }
            
            if (file.size > 10 * 1024 * 1024) {
                alert('文件大小不能超过 10MB');
                return;
            }
            
            selectedFile = file;
            showFileInfo();
            updateButtons();
            updateStatus(`已选择文件: ${file.name}`);
        }
        
        function showFileInfo() {
            fileName.textContent = selectedFile.name;
            fileSize.textContent = `大小: ${(selectedFile.size / 1024 / 1024).toFixed(2)} MB`;
            fileInfo.classList.remove('hidden');
            uploadText.textContent = '文件已选择，点击"开始处理"按钮进行处理';
        }
        
        function updateButtons() {
            selectBtn.classList.toggle('hidden', selectedFile && !isProcessing);
            processBtn.classList.toggle('hidden', !selectedFile || isProcessing);
            resetBtn.classList.toggle('hidden', !selectedFile || isProcessing);
            downloadBtn.classList.toggle('hidden', !userDocId);
        }
        
        function updateStatus(message) {
            status.innerHTML = `<p>${message}</p>`;
        }
        
        function resetUpload() {
            selectedFile = null;
            userDocId = null;
            isProcessing = false;
            fileInput.value = '';
            fileInfo.classList.add('hidden');
            progressContainer.classList.add('hidden');
            uploadText.textContent = '点击选择文件或拖拽文件到此处';
            updateButtons();
            updateStatus('');
        }
        
        async function startProcessing() {
            if (!selectedFile) {
                alert('请先选择文件');
                return;
            }
            
            isProcessing = true;
            updateButtons();
            progressContainer.classList.remove('hidden');
            updateStatus('上传中...');
            
            try {
                // 这里应该调用实际的 API
                updateStatus('模拟上传和处理过程...');
                
                // 模拟进度更新
                for (let i = 0; i <= 100; i += 10) {
                    await new Promise(resolve => setTimeout(resolve, 200));
                    updateProgress(i);
                }
                
                // 模拟完成
                userDocId = 'mock_doc_id_' + Date.now();
                updateStatus('处理完成！可以下载文档了。');
                
            } catch (error) {
                updateStatus('处理失败: ' + error.message);
            } finally {
                isProcessing = false;
                updateButtons();
            }
        }
        
        function updateProgress(percent) {
            progressBar.style.width = percent + '%';
            progressText.textContent = percent + '%';
        }
        
        function downloadDocument() {
            if (!userDocId) {
                alert('没有可下载的文档');
                return;
            }
            
            // 这里应该调用实际的下载 API
            updateStatus('模拟下载文档...');
            setTimeout(() => {
                updateStatus('文档下载完成！');
            }, 1000);
        }
    </script>
</body>
</html>
