from fastapi import APIRouter, HTTPException
from typing import Dict, Any
import httpx
import asyncio
from pydantic import BaseModel
from app.core.ctx import CTX_USER_ID
from app.core.dependency import DependAuth
from app.log import logger

router = APIRouter()


class HumanizerRequest(BaseModel):
    text: str
    lang: str = "Chinese"
    type: str = "zhiwang"


class HumanizerResponse(BaseModel):
    success: bool
    message: str = ""
    data: str = ""
    original_length: int = 0
    humanized_length: int = 0


@router.post("/deai", summary="降AIGC率", response_model=HumanizerResponse, dependencies=[DependAuth])
async def deai_text(
    request: HumanizerRequest,
):
    """
    使用 HumanizerAI 降低 AIGC 检测率
    """
    user_id = CTX_USER_ID.get()
    
    try:
        # 验证输入
        if not request.text or not request.text.strip():
            raise HTTPException(status_code=400, detail="文本内容不能为空")
        
        if len(request.text) > 5000:
            raise HTTPException(status_code=400, detail="文本长度不能超过5000字符")
        
        # 调用 HumanizerAI API
        api_url = "https://api3.speedai.chat/v1/deai"
        
        # 注意：这里需要替换为实际的 API Key
        # 建议从环境变量或配置文件中获取
        api_key = "sk-Z3Snt9PE2nfO5CV6yJNcVHCY"  # 请替换为实际的 API Key
        
        payload = {
            "apikey": api_key,
            "info": request.text,
            "lang": request.lang,
            "type": request.type
        }
        
        headers = {
            "Content-Type": "application/json",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }
        
        async with httpx.AsyncClient(timeout=60.0) as client:
            response = await client.post(api_url, json=payload, headers=headers)
            
            if response.status_code != 200:
                logger.error(f"HumanizerAI API 调用失败: {response.status_code} - {response.text}")
                raise HTTPException(status_code=500, detail="API 调用失败")
            
            result = response.json()
            
            # 根据 API 返回结果调整响应格式
            if result.get("success") or result.get("code") == 200:
                humanized_text = result.get("data", {}).get("text", "") or result.get("text", "")
                
                return HumanizerResponse(
                    success=True,
                    message="降AIGC率处理成功",
                    data=humanized_text,
                    original_length=len(request.text),
                    humanized_length=len(humanized_text)
                )
            else:
                error_msg = result.get("message", "未知错误")
                logger.error(f"HumanizerAI API 返回错误: {error_msg}")
                return HumanizerResponse(
                    success=False,
                    message=f"处理失败: {error_msg}",
                    data="",
                    original_length=len(request.text),
                    humanized_length=0
                )
                
    except httpx.TimeoutException:
        logger.error("HumanizerAI API 请求超时")
        raise HTTPException(status_code=500, detail="请求超时，请稍后重试")
    except httpx.RequestError as e:
        logger.error(f"HumanizerAI API 请求错误: {str(e)}")
        raise HTTPException(status_code=500, detail="网络请求错误")
    except Exception as e:
        logger.error(f"降AIGC率处理异常: {str(e)}")
        raise HTTPException(status_code=500, detail="服务器内部错误")


@router.post("/rewrite", summary="文本重写", response_model=HumanizerResponse, dependencies=[DependAuth])
async def rewrite_text(
    request: HumanizerRequest,
):
    """
    使用 HumanizerAI 重写文本
    """
    user_id = CTX_USER_ID.get()
    
    try:
        # 验证输入
        if not request.text or not request.text.strip():
            raise HTTPException(status_code=400, detail="文本内容不能为空")
        
        if len(request.text) > 5000:
            raise HTTPException(status_code=400, detail="文本长度不能超过5000字符")
        
        # 调用 HumanizerAI API
        api_url = "https://api3.speedai.chat/v1/rewrite"
        
        # 注意：这里需要替换为实际的 API Key
        api_key = "test_api"  # 请替换为实际的 API Key
        
        payload = {
            "apikey": api_key,
            "info": request.text,
            "lang": request.lang,
            "type": request.type
        }
        
        headers = {
            "Content-Type": "application/json",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }
        
        async with httpx.AsyncClient(timeout=60.0) as client:
            response = await client.post(api_url, json=payload, headers=headers)
            
            if response.status_code != 200:
                logger.error(f"HumanizerAI API 调用失败: {response.status_code} - {response.text}")
                raise HTTPException(status_code=500, detail="API 调用失败")
            
            result = response.json()
            
            # 根据 API 返回结果调整响应格式
            if result.get("success") or result.get("code") == 200:
                rewritten_text = result.get("data", {}).get("text", "") or result.get("text", "")
                
                return HumanizerResponse(
                    success=True,
                    message="文本重写处理成功",
                    data=rewritten_text,
                    original_length=len(request.text),
                    humanized_length=len(rewritten_text)
                )
            else:
                error_msg = result.get("message", "未知错误")
                logger.error(f"HumanizerAI API 返回错误: {error_msg}")
                return HumanizerResponse(
                    success=False,
                    message=f"处理失败: {error_msg}",
                    data="",
                    original_length=len(request.text),
                    humanized_length=0
                )
                
    except httpx.TimeoutException:
        logger.error("HumanizerAI API 请求超时")
        raise HTTPException(status_code=500, detail="请求超时，请稍后重试")
    except httpx.RequestError as e:
        logger.error(f"HumanizerAI API 请求错误: {str(e)}")
        raise HTTPException(status_code=500, detail="网络请求错误")
    except Exception as e:
        logger.error(f"文本重写处理异常: {str(e)}")
        raise HTTPException(status_code=500, detail="服务器内部错误")